/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --light-gray: #ecf0f1;
    --medium-gray: #bdc3c7;
    --dark-gray: #34495e;
    --white: #ffffff;
    --black: #2c3e50;
    
    /* Typography */
    --font-family: 'Noto Sans Arabic', Arial, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Base Typography */
body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--black);
    background-color: var(--light-gray);
    direction: rtl;
    text-align: right;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Header Styles */
.main-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    padding: var(--spacing-2xl) 0;
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.header-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header-subtitle {
    font-size: var(--font-size-xl);
    font-weight: 400;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Main Content */
.main-content {
    padding: var(--spacing-2xl) 0;
    min-height: 60vh;
}

/* Loading Indicator */
.loading-indicator {
    text-align: center;
    padding: var(--spacing-3xl) 0;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--light-gray);
    border-top: 4px solid var(--secondary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
    text-align: center;
    padding: var(--spacing-2xl);
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border-right: 4px solid var(--accent-color);
}

.error-message h3 {
    color: var(--accent-color);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-2xl);
}

.retry-btn {
    background-color: var(--secondary-color);
    color: var(--white);
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: var(--transition-normal);
    margin-top: var(--spacing-lg);
}

.retry-btn:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Gallery Grid */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    padding: var(--spacing-lg) 0;
}

/* Image Card */
.image-card {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
    transform: translateY(0);
}

.image-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.image-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 0%, rgba(52, 152, 219, 0.1) 100%);
    opacity: 0;
    transition: var(--transition-normal);
    z-index: 1;
}

.image-card:hover::before {
    opacity: 1;
}

.image-container {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
}

.image-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.image-card:hover img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
    opacity: 0;
    transition: var(--transition-normal);
    display: flex;
    align-items: flex-end;
    padding: var(--spacing-lg);
    z-index: 2;
}

.image-card:hover .image-overlay {
    opacity: 1;
}

.overlay-content {
    color: var(--white);
    text-align: right;
}

.overlay-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.overlay-price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--warning-color);
}

.card-content {
    padding: var(--spacing-lg);
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
    line-height: 1.4;
}

.card-description {
    color: var(--dark-gray);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin-bottom: var(--spacing-lg);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--light-gray);
}

.card-price {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--success-color);
}

.buy-button {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-block;
}

.buy-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--success-color) 100%);
}

/* No Images Message */
.no-images {
    text-align: center;
    padding: var(--spacing-3xl);
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.no-images h3 {
    color: var(--medium-gray);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-2xl);
}

/* Footer */
.main-footer {
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
    margin-top: var(--spacing-3xl);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section h4 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
    color: var(--secondary-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section a {
    color: var(--white);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-section a:hover {
    color: var(--secondary-color);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-links a {
    font-size: var(--font-size-xl);
    transition: var(--transition-fast);
}

.social-links a:hover {
    transform: scale(1.2);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--dark-gray);
    color: var(--medium-gray);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .header-title {
        font-size: var(--font-size-3xl);
    }
    
    .header-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: var(--spacing-lg);
    }
    
    .image-container {
        height: 200px;
    }
    
    .card-content {
        padding: var(--spacing-md);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .gallery-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .header-title {
        font-size: var(--font-size-2xl);
    }
    
    .main-content {
        padding: var(--spacing-lg) 0;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print Styles */
@media print {
    .main-header,
    .main-footer {
        display: none;
    }
    
    .image-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid var(--medium-gray);
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000000;
        --secondary-color: #0066cc;
        --light-gray: #ffffff;
        --medium-gray: #666666;
        --dark-gray: #333333;
    }
}
