# دليل الإعداد السريع - Quick Setup Guide

## خطوات البدء السريع / Quick Start Steps

### 1. تحضير الملفات / Prepare Files
✅ تأكد من وجود جميع الملفات:
- `index.html`
- `styles.css`
- `script.js`
- `config.json`

### 2. تخصيص المحتوى / Customize Content

#### أ. تحديث معلومات الموقع / Update Site Info
في ملف `config.json`:
```json
{
  "siteTitle": "اسم موقعك هنا",
  "siteSubtitle": "وصف موقعك هنا",
  "currency": "جنيه مصري", // أو أي عملة أخرى
  "siteUrl": "https://yourwebsite.com",
  "contactEmail": "<EMAIL>"
}
```

#### ب. إضافة صورك / Add Your Images
استبدل الصور في مصفوفة `images`:
```json
{
  "id": 1,
  "title": "عنوان صورتك",
  "description": "وصف الصورة",
  "imageUrl": "رابط الصورة",
  "price": "السعر",
  "paymentLink": "رابط الدفع الخاص بك"
}
```

### 3. تحديث روابط الدفع / Update Payment Links
🔗 استبدل `https://example.com/buy/X` بروابط الدفع الحقيقية:
- PayPal
- Stripe
- Square
- أي منصة دفع أخرى

### 4. تحسين SEO / SEO Optimization

#### أ. تحديث Meta Tags في index.html
```html
<title>عنوان موقعك - كلمات مفتاحية</title>
<meta name="description" content="وصف موقعك هنا">
<meta name="keywords" content="كلماتك المفتاحية هنا">
```

#### ب. تحديث Open Graph
```html
<meta property="og:title" content="عنوان موقعك">
<meta property="og:description" content="وصف موقعك">
<meta property="og:url" content="https://yourwebsite.com">
```

### 5. اختبار الموقع / Test Website

#### أ. اختبار محلي / Local Testing
1. افتح `index.html` في المتصفح
2. تأكد من ظهور الصور
3. اختبر روابط الدفع
4. تأكد من التصميم المتجاوب

#### ب. اختبار الأداء / Performance Testing
- استخدم Google PageSpeed Insights
- اختبر على أجهزة مختلفة
- تأكد من سرعة التحميل

### 6. النشر / Deployment

#### خيارات الاستضافة المجانية / Free Hosting Options
1. **GitHub Pages**
   - ارفع الملفات إلى repository
   - فعل GitHub Pages في الإعدادات

2. **Netlify**
   - اسحب وأفلت المجلد
   - أو اربط مع GitHub

3. **Vercel**
   - استورد من GitHub
   - أو ارفع الملفات مباشرة

4. **Firebase Hosting**
   - استخدم Firebase CLI
   - `firebase deploy`

### 7. بعد النشر / After Deployment

#### أ. تحديث الروابط / Update Links
- غير `https://yourwebsite.com` إلى رابط موقعك الحقيقي
- حدث sitemap.xml
- حدث robots.txt

#### ب. إعداد Analytics / Setup Analytics
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

## نصائح مهمة / Important Tips

### 🖼️ تحسين الصور / Image Optimization
- استخدم تنسيق WebP للصور الحديثة
- اضغط الصور قبل الرفع
- استخدم أحجام مناسبة (1000px عرض كحد أقصى)

### 💳 روابط الدفع / Payment Links
- اختبر جميع روابط الدفع قبل النشر
- استخدم HTTPS دائماً
- أضف صفحة شكر بعد الدفع

### 📱 التصميم المتجاوب / Responsive Design
- اختبر على الهاتف والتابلت
- تأكد من وضوح النصوص
- اختبر سهولة النقر على الأزرار

### 🔍 محركات البحث / Search Engines
- أرسل sitemap.xml إلى Google Search Console
- استخدم كلمات مفتاحية عربية مناسبة
- اكتب أوصاف جذابة للصور

## استكشاف الأخطاء الشائعة / Common Issues

### ❌ الصور لا تظهر
**الحل:**
1. تحقق من صحة روابط الصور
2. تأكد من أن الصور متاحة للعامة
3. اختبر الروابط في متصفح منفصل

### ❌ التصميم مكسور
**الحل:**
1. تأكد من تحميل ملف styles.css
2. تحقق من وجود أخطاء في console
3. اختبر في متصفحات مختلفة

### ❌ روابط الدفع لا تعمل
**الحل:**
1. تأكد من صحة الروابط
2. اختبر الروابط يدوياً
3. تحقق من إعدادات منصة الدفع

## قائمة التحقق النهائية / Final Checklist

- [ ] تم تحديث جميع المعلومات في config.json
- [ ] تم اختبار جميع روابط الدفع
- [ ] تم تحسين جميع الصور
- [ ] تم تحديث meta tags
- [ ] تم اختبار التصميم المتجاوب
- [ ] تم رفع الموقع للاستضافة
- [ ] تم تحديث sitemap.xml و robots.txt
- [ ] تم إعداد Google Analytics
- [ ] تم إرسال الموقع لمحركات البحث

---

**🎉 تهانينا! موقعك جاهز للعمل**
**🎉 Congratulations! Your website is ready to go**

للدعم الفني، راجع ملف README.md أو تواصل معنا.
For technical support, check README.md or contact us.
