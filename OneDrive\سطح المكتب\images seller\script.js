// Image Gallery Marketplace JavaScript
// Author: Image Gallery Marketplace
// Description: Handles dynamic image loading, click events, and error management

class ImageGallery {
    constructor() {
        this.images = [];
        this.config = {};
        this.loadingIndicator = document.getElementById('loadingIndicator');
        this.errorMessage = document.getElementById('errorMessage');
        this.galleryGrid = document.getElementById('galleryGrid');
        this.noImagesMessage = document.getElementById('noImagesMessage');
        this.headerTitle = document.getElementById('headerTitle');
        this.headerSubtitle = document.getElementById('headerSubtitle');
        
        this.init();
    }

    async init() {
        try {
            await this.loadConfiguration();
            await this.loadImages();
            this.updateHeaderContent();
            this.renderImages();
            this.updateStructuredData();
        } catch (error) {
            console.error('Error initializing gallery:', error);
            this.showError();
        }
    }

    async loadConfiguration() {
        try {
            // Try to load external config file first
            const response = await fetch('config.json');
            if (response.ok) {
                this.config = await response.json();
                this.images = this.config.images || [];
            } else {
                // Fallback to default configuration
                this.loadDefaultConfiguration();
            }
        } catch (error) {
            console.warn('Could not load config.json, using default configuration');
            this.loadDefaultConfiguration();
        }
    }

    loadDefaultConfiguration() {
        this.config = {
            siteTitle: "معرض الصور المميزة",
            siteSubtitle: "اكتشف مجموعة رائعة من الصور عالية الجودة",
            currency: "جنيه مصري",
            images: [
                {
                    id: 1,
                    title: "القمر الصامت",
                    description: "جلس القمرُ وحيدًا في منتصف السماء، يهمسُ الليلَ بصدقٍ رقيقٍ ورسائل حنينٍ لا تصل. كان كلُّ ضوءٍ ينسابُ منهُ يلفُّظُ ألمًا صغيرًا. القمر الصامت (متبقي عدد محدود)",
                    imageUrl: "images/Moon.jpg",
                    altText: "جلس القمرُ وحيدًا في منتصف السماء، يهمسُ الليلَ بصدقٍ رقيقٍ ورسائل حنينٍ لا تصل. كان كلُّ ضوءٍ ينسابُ منهُ يلفُّظُ ألمًا صغيرًا، ثم يبتسمُ بصمتٍ باردٍ.",
                    price: "645.94",
                    paymentLink: "https://ipn.eg/S/omar7sn/instapay/1wmKEO",
                    category: "طبيعة",
                    tags: ["طبيعة", "فضاء", "سماء", "قمر"]
                },
                {
                    id: 2,
                    title: "درب الصيادين",
                    description: "لم يكن درب الصيادين مجرد طريقٍ، بل كان قصةً تُروى كلَّ فجرٍ. كانت خطواتُ الصيادينَ القدامى محفورةً في ترابهِ، وكلُّ حجرٍ فيهِ يحكي عن صبرٍ لا ينفدُ.",
                    imageUrl: "images/Hunters road.jpg",
                    altText: "لم يكن درب الصيادين مجرد طريقٍ، بل كان قصةً تُروى كلَّ فجرٍ. كانت خطواتُ الصيادينَ القدامى محفورةً في ترابهِ، وكلُّ حجرٍ فيهِ يحكي عن صبرٍ لا ينفدُ.",
                    price: "968.80",
                    paymentLink: "https://ipn.eg/S/omar7sn/instapay/1wmKEO",
                    category: "طبيعة",
                    tags: ["بحر", "صيادين", "مركب", "سماء"]
                },
                {
                    id: 3,
                    title: "خرير الماء",
                    description: "خريرُ ماءِ البحرِ هوَ دعوةٌ للتأملِ، يُشبهُ تنهيدةَ الطبيعةِ العميقةَ، تُخبرنا بأنَّ لكلِّ شيءٍ في هذا الكونِ إيقاعَهُ الخاصَّ، حتى في أوسعِ مساحاتِهِ وأكثرِها سكونًا.",
                    imageUrl: "images/Water Sound.jpg",
                    altText: "خريرُ ماءِ البحرِ هوَ دعوةٌ للتأملِ، يُشبهُ تنهيدةَ الطبيعةِ العميقةَ، تُخبرنا بأنَّ لكلِّ شيءٍ في هذا الكونِ إيقاعَهُ الخاصَّ، حتى في أوسعِ مساحاتِهِ وأكثرِها سكونًا.",
                    price: "1291.87",
                    paymentLink: "https://ipn.eg/S/omar7sn/instapay/1wmKEO",
                    category: "طبيعة",
                    tags: ["ماء", "رمال", "بحر", "خرير"]
                },
                {
                    id: 4,
                    title: "الإنتظار ",
                    description: "توقفَ الزمنُ، وأصبحَ الصمتُ هو اللغةَ الوحيدةَ. كانَ هدوءٌ عميقٌ يلفُّ المكانَ، لا شيءَ يُسمعُ سوى نبضاتِ القلبِ، التي أصبحتْ إيقاعًا يُسيطرُ على كلِّ شيءٍ. لم يكنْ صمتًا مخيفًا، بل كانَ سكونًا مُريحًا، يسمحُ للأفكارِ أن تتنفسَ، وللأرواحِ أن تتكلمَ دونَ حاجةٍ للكلماتِ. في هذا الهدوءِ، كلُّ شيءٍ يُصبحُ أكثرَ وضوحًا، وأكثرَ جمالًا.",
                    imageUrl: "images/Waiting.jpg",
                    altText: "توقفَ الزمنُ، وأصبحَ الصمتُ هو اللغةَ الوحيدةَ. كانَ هدوءٌ عميقٌ يلفُّ المكانَ، لا شيءَ يُسمعُ سوى نبضاتِ القلبِ، التي أصبحتْ إيقاعًا يُسيطرُ على كلِّ شيءٍ. لم يكنْ صمتًا مخيفًا، بل كانَ سكونًا مُريحًا، يسمحُ للأفكارِ أن تتنفسَ، وللأرواحِ أن تتكلمَ دونَ حاجةٍ للكلماتِ. في هذا الهدوءِ، كلُّ شيءٍ يُصبحُ أكثرَ وضوحًا، وأكثرَ جمالًا.",
                    price: "516.91",
                    paymentLink: "https://ipn.eg/S/omar7sn/instapay/1wmKEO",
                    category: "طبيعة",
                    tags: ["مياه", "أنتطار", "بحر", "سفينة"]
                }
            ]
        };
        this.images = this.config.images;
    }

    async loadImages() {
        // Simulate loading time for better UX
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Validate images
        this.images = this.images.filter(image => this.validateImage(image));
        
        if (this.images.length === 0) {
            throw new Error('No valid images found');
        }
    }

    validateImage(image) {
        const requiredFields = ['id', 'title', 'imageUrl', 'paymentLink'];
        return requiredFields.every(field => image[field] && image[field].toString().trim() !== '');
    }

    updateHeaderContent() {
        if (this.config.siteTitle) {
            this.headerTitle.textContent = this.config.siteTitle;
            document.title = this.config.siteTitle;
        }
        
        if (this.config.siteSubtitle) {
            this.headerSubtitle.textContent = this.config.siteSubtitle;
        }
    }

    renderImages() {
        this.hideLoading();
        
        if (this.images.length === 0) {
            this.showNoImages();
            return;
        }

        this.galleryGrid.innerHTML = '';
        
        this.images.forEach((image, index) => {
            const imageCard = this.createImageCard(image, index);
            this.galleryGrid.appendChild(imageCard);
        });

        // Add entrance animation
        this.animateCards();
    }

    createImageCard(image, index) {
        const card = document.createElement('div');
        card.className = 'image-card';
        card.setAttribute('data-image-id', image.id);
        card.style.animationDelay = `${index * 0.1}s`;
        
        const currency = this.config.currency || 'ريال';
        
        card.innerHTML = `
            <div class="image-container">
                <img src="${image.imageUrl}" 
                     alt="${image.altText || image.title}" 
                     loading="lazy"
                     onerror="this.parentElement.parentElement.style.display='none'">
                <div class="image-overlay">
                    <div class="overlay-content">
                        <div class="overlay-title">${image.title}</div>
                        <div class="overlay-price">${image.price} ${currency}</div>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <h3 class="card-title">${image.title}</h3>
                <p class="card-description">${image.description || ''}</p>
                <div class="card-footer">
                    <span class="card-price">${image.price} ${currency}</span>
                    <button class="buy-button" onclick="window.open('${image.paymentLink}', '_blank')">
                        شراء الآن
                    </button>
                </div>
            </div>
        `;

        // Add click event to the entire card
        card.addEventListener('click', (e) => {
            // Don't trigger if clicking on the buy button
            if (!e.target.classList.contains('buy-button')) {
                this.handleImageClick(image);
            }
        });

        return card;
    }

    handleImageClick(image) {
        // Track click event (you can integrate with analytics here)
        this.trackImageClick(image);
        
        // Redirect to payment link
        window.open(image.paymentLink, '_blank');
    }

    trackImageClick(image) {
        // Analytics tracking (integrate with Google Analytics, etc.)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'image_click', {
                'image_id': image.id,
                'image_title': image.title,
                'image_price': image.price
            });
        }
        
        console.log('Image clicked:', image.title);
    }

    animateCards() {
        const cards = document.querySelectorAll('.image-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    updateStructuredData() {
        const structuredData = {
            "@context": "https://schema.org",
            "@type": "ItemList",
            "name": "مجموعة الصور المميزة",
            "description": "مجموعة من الصور عالية الجودة المتاحة للشراء",
            "numberOfItems": this.images.length,
            "itemListElement": this.images.map((image, index) => ({
                "@type": "Product",
                "position": index + 1,
                "name": image.title,
                "description": image.description,
                "image": image.imageUrl,
                "offers": {
                    "@type": "Offer",
                    "price": image.price,
                    "priceCurrency": "SAR",
                    "availability": "https://schema.org/InStock",
                    "url": image.paymentLink
                }
            }))
        };

        // Update existing structured data script
        const existingScript = document.querySelector('script[type="application/ld+json"]:last-of-type');
        if (existingScript) {
            existingScript.textContent = JSON.stringify(structuredData);
        }
    }

    hideLoading() {
        this.loadingIndicator.style.display = 'none';
    }

    showError() {
        this.hideLoading();
        this.errorMessage.style.display = 'block';
        this.galleryGrid.style.display = 'none';
    }

    showNoImages() {
        this.hideLoading();
        this.noImagesMessage.style.display = 'block';
        this.galleryGrid.style.display = 'none';
    }
}

// Utility Functions
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

function formatPrice(price, currency = 'ريال') {
    return `${price} ${currency}`;
}

// Initialize gallery when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ImageGallery();
});

// Handle window resize for responsive adjustments
window.addEventListener('resize', debounce(() => {
    // Add any resize-specific logic here
    console.log('Window resized');
}, 250));

// Debounce utility function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Service Worker Registration (for PWA capabilities)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
                console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ImageGallery };
}
